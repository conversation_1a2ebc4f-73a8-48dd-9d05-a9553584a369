from modelscope.hub.api import HubApi

YOUR_ACCESS_TOKEN = 'ms-************************************'
api = HubApi()
api.login(YOUR_ACCESS_TOKEN)

owner_name = 'zsq857867246'
dataset_name = 'math-weight'

api.upload_file(
    path_or_fileobj='/home/<USER>/ZhouSQ/DCX/TACL_math1/_30shot_none_171_embed_doc_0.pkl',
    path_in_repo='zsq857867246/math-weight/pkl',
    repo_id=f"{owner_name}/{dataset_name}",
    repo_type = 'dataset',
    commit_message='upload dataset file to repo',
)