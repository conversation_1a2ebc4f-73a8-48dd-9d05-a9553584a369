import os
import argparse
from datetime import datetime
from tqdm import tqdm
import torch
import openprompt
from openprompt.prompts import <PERSON>Verbalizer, ManualTemplate

from processor import PROCESSOR
from processor_des import PROCESSOR1

from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader
from transformers.optimization import get_linear_schedule_with_warmup
from torch.optim import AdamW

# Models (use the training model for both training and embedding extraction)
from models.hierVerb import HierVerbPromptForClassification as TrainModel


def build_loaders(args, tokenizer, WrapperClass, processor, dataset):
    max_seq_l = args.max_seq_lens
    eval_batch_s = 16

    if args.multi_mask:
        template_file = f"{args.dataset}_mask_template.txt"
    else:
        template_file = "manual_template.txt"
    template_path = "template"
    text_mask = [f"{i + 1} level: {{\"mask\"}}" for i in range(args.depth)]
    text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
    os.makedirs(template_path, exist_ok=True)
    if not os.path.exists("ckpts"):
        os.mkdir("ckpts")
    template_path = os.path.join(template_path, template_file)
    if not os.path.exists(template_path):
        with open(template_path, 'w', encoding='utf-8') as fp:
            fp.write(text)
    mytemplate = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)

    train_loader = SinglePathPromptDataLoader(
        dataset=dataset['train'], template=mytemplate, tokenizer=tokenizer,
        tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
        decoder_max_length=3, batch_size=args.batch_size, shuffle=args.shuffle,
        teacher_forcing=False, predict_eos_token=False, truncate_method="tail",
        num_works=4, multi_gpu=args.use_multi_gpu,
    )

    if args.dataset == "wos":
        full_name = "WebOfScience"
    elif args.dataset == "dbp":
        full_name = "DBpedia"
    else:
        raise NotImplementedError

    test_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset", full_name, f"test_dataloader-multi_mask.pt")
    dev_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset", full_name, f"dev_dataloader-multi_mask.pt")

    if args.dataset != "dbp" and os.path.exists(dev_path):
        valid_loader = torch.load(dev_path, weights_only=False)
    else:
        valid_loader = SinglePathPromptDataLoader(
            dataset=dataset['dev'], template=mytemplate, tokenizer=tokenizer,
            tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
            decoder_max_length=3, batch_size=eval_batch_s, shuffle=False,
            teacher_forcing=False, predict_eos_token=False, truncate_method="tail",
            multi_gpu=args.use_multi_gpu,
        )
        if args.dataset != "dbp":
            torch.save(valid_loader, dev_path)

    if not os.path.exists(test_path):
        test_loader = SinglePathPromptDataLoader(
            dataset=dataset['test'], template=mytemplate, tokenizer=tokenizer,
            tokenizer_wrapper_class=WrapperClass, max_seq_length=max_seq_l,
            decoder_max_length=3, batch_size=eval_batch_s, shuffle=False,
            teacher_forcing=False, predict_eos_token=False, truncate_method="tail",
            multi_gpu=args.use_multi_gpu, mode='test',
        )
        torch.save(test_loader, test_path)
    else:
        test_loader = torch.load(test_path, weights_only=False)

    return mytemplate, train_loader, valid_loader, test_loader


def build_model(args, plm, tokenizer, template, processor, use_cuda):
    # build verbalizers
    verbalizer_list = []
    for i in range(args.depth):
        verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=processor.label_list[i]))

    print_info("loading prompt model")
    model = TrainModel(
        plm=plm, template=template, verbalizer_list=verbalizer_list, tokenizer=tokenizer,
        freeze_plm=args.freeze_plm, args=args, processor=processor,
        plm_eval_mode=args.plm_eval_mode, use_cuda=use_cuda,
    )
    if use_cuda and not args.use_multi_gpu:
        model = model.cuda()
    elif args.use_multi_gpu:
        print_info("Model distributed across multiple GPUs via device_map='auto'")
    return model


def setup_optim(args, model, train_loader):
    no_decay = ['bias', 'LayerNorm.weight']
    named_parameters = model.plm.named_parameters()
    optimizer_grouped_parameters1 = [
        {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)], 'weight_decay': 0.01},
        {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)], 'weight_decay': 0.0}
    ]
    verbalizer = model.verbalizer
    optimizer_grouped_parameters2 = [
        {'params': verbalizer.group_parameters_1, "lr": args.lr},
        {'params': verbalizer.group_parameters_2, "lr": args.lr2},
    ]
    optimizer1 = AdamW(optimizer_grouped_parameters1, lr=args.lr)
    optimizer2 = AdamW(optimizer_grouped_parameters2)
    tot_step = len(train_loader) // args.gradient_accumulation_steps * args.max_epochs
    warmup_steps = 0
    scheduler1 = get_linear_schedule_with_warmup(optimizer1, num_warmup_steps=warmup_steps, num_training_steps=tot_step) if args.use_scheduler1 else None
    scheduler2 = get_linear_schedule_with_warmup(optimizer2, num_warmup_steps=warmup_steps, num_training_steps=tot_step) if args.use_scheduler2 else None
    return optimizer1, optimizer2, scheduler1, scheduler2


def eval_classification(model, dataloader, processor, args, desc="Valid"):
    scores = model.evaluate(dataloader, processor, desc=desc, mode=args.eval_mode)
    return scores.get('macro_f1', 0.0), scores.get('micro_f1', 0.0)


def compute_embedding_store(model, dataloader, device, processor):
    model.eval()
    embeddings = []
    labels = []
    pbar = tqdm(dataloader, desc="BuildEmbed(train)")
    for batch in pbar:
        if hasattr(batch, 'cuda'):
            batch = batch.cuda()
        else:
            batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
            batch = {"input_ids": batch[0], "attention_mask": batch[1], "label": batch[2], "loss_ids": batch[3]}
        with torch.no_grad():
            logits, outputs_at_mask = model(batch)
        # outputs_at_mask: [B, depth, H]; take last depth as leaf embedding
        leaf = outputs_at_mask[:, -1, :] if isinstance(outputs_at_mask, torch.Tensor) else outputs_at_mask[-1]
        embeddings.append(leaf)
        labels.append(batch['label'])
    embeddings = torch.cat(embeddings, dim=0)
    labels = torch.cat(labels, dim=0)
    return {"embedding": embeddings, "label": labels}


def topk_eval(model, query_loader, processor, train_store, topk, device, desc="TopKValid"):
    import torch.nn.functional as F
    model.eval()
    hier_mapping = processor.hier_mapping
    depth = len(hier_mapping) + 1
    all_length = len(processor.all_labels)
    total_label_num = all_length
    tp = torch.zeros(total_label_num)
    fp = torch.zeros(total_label_num)
    fn = torch.zeros(total_label_num)
    true_leaf_labels, pred_leaf_labels = [], []

    pbar = tqdm(query_loader, desc=desc)
    for batch in pbar:
        if hasattr(batch, 'cuda'):
            batch = batch.cuda()
        else:
            batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
            batch = {"input_ids": batch[0], "attention_mask": batch[1], "label": batch[2], "loss_ids": batch[3]}
        with torch.no_grad():
            logits, outputs_at_mask = model(batch)
        leaf_embed = outputs_at_mask  # [B, depth, H]
        leaf_labels = batch['label']

        for p in range(leaf_embed.shape[0]):
            # build true path onehot indices
            if depth == 2:
                true_label = [hier_mapping[0][1][leaf_labels[p].item()], leaf_labels[p].item() + len(processor.label_list[0])]
            elif depth == 3:
                true_label = [hier_mapping[0][1][hier_mapping[1][1][leaf_labels[p].item()]],
                              hier_mapping[1][1][leaf_labels[p].item()] + len(processor.label_list[0]),
                              leaf_labels[p].item() + len(processor.label_list[0]) + len(processor.label_list[1])]
            else:
                # general case
                idxs = [leaf_labels[p].item()]
                for lv in range(depth-2, -1, -1):
                    idxs.insert(0, hier_mapping[lv][1][idxs[0]])
                offsets = [0]
                for lv in range(depth-1):
                    offsets.append(offsets[-1] + len(processor.label_list[lv]))
                true_label = [idxs[i] + offsets[i] for i in range(depth)]

            true_onehot = torch.zeros(total_label_num)
            true_onehot[true_label] = 1
            sim = F.cosine_similarity(leaf_embed[p][-1].view(1, -1), train_store['embedding'])
            top_vals, top_idx = torch.topk(sim, k=topk)

            best_pred_label, best_tp_sum, best_leaf_pred = None, -1, None
            for q in range(len(top_idx)):
                similar_leaf = train_store['label'][top_idx[q]].item()
                # map to path indices
                if depth == 2:
                    pred_label = [hier_mapping[0][1][similar_leaf], similar_leaf + len(processor.label_list[0])]
                else:
                    idxs = [similar_leaf]
                    for lv in range(depth-2, -1, -1):
                        idxs.insert(0, hier_mapping[lv][1][idxs[0]])
                    offsets = [0]
                    for lv in range(depth-1):
                        offsets.append(offsets[-1] + len(processor.label_list[lv]))
                    pred_label = [idxs[i] + offsets[i] for i in range(depth)]
                pred_onehot = torch.zeros(total_label_num)
                pred_onehot[pred_label] = 1
                tp_sum = torch.sum(pred_onehot * true_onehot).item()
                if tp_sum > best_tp_sum:
                    best_tp_sum = tp_sum
                    best_pred_label = pred_label
                    best_leaf_pred = similar_leaf

            if best_pred_label is not None:
                pred_onehot = torch.zeros(total_label_num)
                pred_onehot[best_pred_label] = 1
                tp += torch.sum(pred_onehot * true_onehot, dim=0)
                fp += torch.sum(pred_onehot * (1 - true_onehot), dim=0)
                fn += torch.sum((1 - pred_onehot) * true_onehot, dim=0)
                true_leaf_labels.append(leaf_labels[p].item())
                pred_leaf_labels.append(best_leaf_pred)

    eps = 1e-16
    micro_p = torch.sum(tp).item() / (torch.sum(tp).item() + torch.sum(fp).item() + eps)
    micro_r = torch.sum(tp).item() / (torch.sum(tp).item() + torch.sum(fn).item() + eps)
    micro_f1 = 2 * micro_p * micro_r / (micro_p + micro_r + eps)

    macro_p = tp / (tp + fp + eps)
    macro_r = tp / (tp + fn + eps)
    macro_p = torch.where(torch.isnan(macro_p), torch.zeros_like(macro_p), macro_p)
    macro_r = torch.where(torch.isnan(macro_r), torch.zeros_like(macro_r), macro_r)
    macro_f1 = 2 * macro_p * macro_r / (macro_p + macro_r + eps)
    macro_f1 = torch.where(torch.isnan(macro_f1), torch.zeros_like(macro_f1), macro_f1)

    if len(true_leaf_labels) > 0:
        present_labels = torch.unique(torch.tensor(true_leaf_labels))
        macro_f1 = torch.mean(macro_f1[present_labels]).item()
    else:
        macro_f1 = 0.0

    return macro_f1, micro_f1


def main():
    start_time = datetime.now()
    parser = argparse.ArgumentParser("Joint train-embed-topk")

    # Base args (aligned with train_tb defaults)
    parser.add_argument("--model", type=str, default='bert')
    parser.add_argument("--model_name_or_path", default='/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext')
    parser.add_argument("--result_file", type=str, default="/home/<USER>/ZhouSQ/DCX/TACL_math1/result/joint_train.txt")

    parser.add_argument("--multi_label", default=0, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)
    parser.add_argument("--constraint_loss", default=0, type=int)
    parser.add_argument("--constraint_alpha", default=0.9, type=float)
    parser.add_argument("--lm_training", default=1, type=int)
    parser.add_argument("--lm_alpha", default=0.7, type=float)
    parser.add_argument("--lr", default=3e-5, type=float)
    parser.add_argument("--lr2", default=1e-4, type=float)
    parser.add_argument("--contrastive_loss", default=0, type=int)
    parser.add_argument("--contrastive_alpha", default=0.9, type=float)
    parser.add_argument("--contrastive_level", default=1, type=int)
    parser.add_argument("--batch_size", default=64, type=int)
    parser.add_argument("--depth", default=9, type=int)
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)
    parser.add_argument("--contrastive_logits", default=1, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)
    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)
    parser.add_argument("--imbalanced_weight", default=True, type=bool)
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
    parser.add_argument("--device", default=1, type=int)
    parser.add_argument("--use_multi_gpu", default=True, type=bool)
    parser.add_argument("--use_fp16", default=False, type=bool)
    parser.add_argument("--force_use_all_gpus", default=False, type=bool)
    parser.add_argument("--max_grad_norm", default=1.0, type=float)
    parser.add_argument("--max_seq_lens", default=512, type=int)
    parser.add_argument("--use_new_ct", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)
    parser.add_argument('--mean_verbalizer', default=True, type=bool)
    parser.add_argument("--shot", type=int, default=30)
    parser.add_argument("--label_description", type=int, default=0)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--verbalizer", type=str, default="soft")
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--not_manual", default=False, type=int)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=1)
    parser.add_argument("--max_epochs", type=int, default=80)

    # Joint control
    parser.add_argument("--eval_every", type=int, default=1, help="Evaluate every N epochs")
    parser.add_argument("--patience", type=int, default=1, help="How many failed evals before early stop")
    parser.add_argument("--topk", type=int, default=1, help="Top-K for retrieval eval")
    parser.add_argument("--eval_split", type=str, default="dev", choices=["dev", "test"], help="Which split to evaluate on")

    args = parser.parse_args()

    # device setup
    if args.use_multi_gpu:
        print_info("Using multi-GPU mode with device_map='auto'")
        use_cuda = True
        device = torch.device("cuda")
    elif args.device != -1 and torch.cuda.is_available():
        os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
        device = torch.device("cuda:0")
        use_cuda = True
        print_info(f"Using single GPU mode: GPU {args.device}")
    else:
        use_cuda = False
        device = torch.device("cpu")
        print_info("Using CPU mode")

    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    args.shuffle = True if args.shuffle == 1 else False

    # data
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)
    if args.label_description:
        processor1 = PROCESSOR1[args.dataset](shot=args.shot, seed=args.seed)
        dataset = {'train': processor1.train_example, 'dev': processor1.dev_example, 'test': processor1.test_example}
    else:
        dataset = {'train': processor.train_example, 'dev': processor.dev_example, 'test': processor.test_example}
    dataset = {k: [[i.text_a, i.label] for i in v] for k, v in dataset.items()}

    args.depth = len(processor.hier_mapping) + 1
    args.template_id = 0

    print_info(f"final train/dev/test lens: {len(dataset['train'])}/{len(dataset['dev'])}/{len(dataset['test'])}")

    # plm & loaders
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    template, train_loader, valid_loader, test_loader = build_loaders(args, tokenizer, WrapperClass, processor, dataset)

    model = build_model(args, plm, tokenizer, template, processor, use_cuda)
    optimizer1, optimizer2, scheduler1, scheduler2 = setup_optim(args, model, train_loader)

    # run id
    current_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    run_id = f"{current_time}-lr-{args.lr}-lm_training-{args.lm_training}-lm_alpha-{args.lm_alpha}-bs-{args.batch_size}"
    print_info(f"saved_path: {run_id}")

    # best tracking
    best = {
        'clf_macro': -1.0, 'clf_micro': -1.0,
        'emb_macro': -1.0, 'emb_micro': -1.0,
        'topk_macro': -1.0, 'topk_micro': -1.0,
    }
    bad_eval_count = 0

    target_loader = valid_loader if args.eval_split == 'dev' else test_loader

    for epoch in range(args.max_epochs):
        print_info(f"------------ epoch {epoch+1} ------------")
        # train one epoch
        model.train()
        loss_detailed = [0, 0, 0, 0]
        for batch in tqdm(train_loader, desc="Train"):
            batch = tuple(t.to(device) if isinstance(t, torch.Tensor) else t for t in batch)
            batch = {"input_ids": batch[0], "attention_mask": batch[1], "label": batch[2], "loss_ids": batch[3]}
            logits, loss, cur_loss_detailed = model(batch)
            loss_detailed = [loss_detailed[i] + v for i, v in enumerate(cur_loss_detailed)]
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), args.max_grad_norm)
            optimizer1.step(); optimizer2.step()
            if scheduler1 is not None: scheduler1.step()
            if scheduler2 is not None: scheduler2.step()
            optimizer1.zero_grad(); optimizer2.zero_grad()
        print_info(f"loss details (multi-verb, lm, constraint, contrastive): {loss_detailed}")

        # periodic eval
        if (epoch + 1) % args.eval_every == 0:
            # 1) classification eval
            clf_macro, clf_micro = eval_classification(model, target_loader, processor, args, desc=f"{args.eval_split.upper()}-CLF")
            print_info(f"[CLF] macro {clf_macro:.4f} micro {clf_micro:.4f}")

            # 2) build train embedding store (from current model)
            train_store = compute_embedding_store(model, train_loader, device, processor)
            # also compute embedding-based classification scores on eval split (reuse evaluate)
            emb_macro, emb_micro = eval_classification(model, target_loader, processor, args, desc=f"{args.eval_split.upper()}-EMB")
            print_info(f"[EMB] macro {emb_macro:.4f} micro {emb_micro:.4f}")

            # 3) top-k retrieval eval against train embeddings
            topk_macro, topk_micro = topk_eval(model, target_loader, processor, train_store, args.topk, device, desc=f"{args.eval_split.upper()}-TOPK@{args.topk}")
            print_info(f"[TOPK] macro {topk_macro:.4f} micro {topk_micro:.4f}")

            all_improved = (
                clf_macro > best['clf_macro'] and clf_micro > best['clf_micro'] and
                emb_macro > best['emb_macro'] and emb_micro > best['emb_micro'] and
                topk_macro > best['topk_macro'] and topk_micro > best['topk_micro']
            )

            if all_improved:
                best.update({
                    'clf_macro': clf_macro, 'clf_micro': clf_micro,
                    'emb_macro': emb_macro, 'emb_micro': emb_micro,
                    'topk_macro': topk_macro, 'topk_micro': topk_micro,
                })
                # save checkpoint
                ckpt_path = f"ckpts/{run_id}-epoch{epoch+1}-best_all.ckpt"
                torch.save(model.state_dict(), ckpt_path)
                print_info(f"Saved improved checkpoint to {ckpt_path}")
                bad_eval_count = 0
            else:
                bad_eval_count += 1
                print_info(f"No overall improvement. bad_eval_count={bad_eval_count}/{args.patience}")
                if bad_eval_count >= args.patience:
                    print_info("Early stop triggered.")
                    break

    # Final summary
    print_info("=" * 40)
    print_info(f"start_time {start_time}")
    print_info(f"end_time {datetime.now()}")
    print_info(f"Best metrics: {best}")


if __name__ == "__main__":
    main()

