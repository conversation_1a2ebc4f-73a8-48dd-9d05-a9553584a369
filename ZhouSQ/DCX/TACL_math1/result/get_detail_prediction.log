--- Log Start: 2025-08-12 14:11:28 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 11171 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 11171 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 11171
   - 总知识点关联数: 11171
🚀 开始批量预测...
🔄 开始批量预测 11171 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 11171
   - 成功预测数量: 11171
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 76.29%
   - 完全匹配题目: 8522
   - 部分匹配题目: 0
   - 无匹配题目: 2649

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 11171 题）：
   - 命中 1 个知识点: 8522题，占比 76.29%
   - 未命中任何知识点: 2649题，占比 23.71%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！


📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 11171
   - 成功预测数量: 11171
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 76.61%
   - 完全匹配题目: 8558
   - 部分匹配题目: 0
   - 无匹配题目: 2613

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 11171 题）：
   - 命中 1 个知识点: 8558题，占比 76.61%
   - 未命中任何知识点: 2613题，占比 23.39%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-3.json

🏁 测试完成！
--- Log Start: 2025-08-14 16:28:44 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 1024])
depth 1: torch.Size([5, 1024])
depth 2: torch.Size([29, 1024])
depth 3: torch.Size([217, 1024])
depth 4: torch.Size([893, 1024])
depth 5: torch.Size([3577, 1024])
depth 6: torch.Size([9097, 1024])
depth 7: torch.Size([14989, 1024])
depth 8: torch.Size([15008, 1024])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 23.46%
   - 完全匹配题目: 2470
   - 部分匹配题目: 0
   - 无匹配题目: 8059

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 2470题，占比 23.46%
   - 未命中任何知识点: 8059题，占比 76.54%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-15 16:36:10 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 82.85%
   - 完全匹配题目: 8723
   - 部分匹配题目: 0
   - 无匹配题目: 1806

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 8723题，占比 82.85%
   - 未命中任何知识点: 1806题，占比 17.15%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
--- Log Start: 2025-08-15 17:45:38 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 81.10%
   - 完全匹配题目: 8539
   - 部分匹配题目: 0
   - 无匹配题目: 1990

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 8539题，占比 81.10%
   - 未命中任何知识点: 1990题，占比 18.90%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！
--- Log Start: 2025-08-18 09:45:19 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 64.21%
   - 完全匹配题目: 6761
   - 部分匹配题目: 0
   - 无匹配题目: 3768

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 6761题，占比 64.21%
   - 未命中任何知识点: 3768题，占比 35.79%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-19 09:26:09 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
✅ 启用了DataParallel，使用4张GPU进行推理
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 45.29%
   - 完全匹配题目: 4769
   - 部分匹配题目: 0
   - 无匹配题目: 5760

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 4769题，占比 45.29%
   - 未命中任何知识点: 5760题，占比 54.71%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
--- Log Start: 2025-08-27 10:43:11 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
✅ 启用了DataParallel，使用4张GPU进行推理
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 51.38%
   - 完全匹配题目: 5410
   - 部分匹配题目: 0
   - 无匹配题目: 5119

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 5410题，占比 51.38%
   - 未命中任何知识点: 5119题，占比 48.62%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！
--- Log Start: 2025-08-29 18:04:02 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
❌ 初始化模型组件失败: Can't load the configuration of '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext' is the correct path to a directory containing a config.json file
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 476, in cached_files
    hf_hub_download(
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 708, in _get_config_dict
    resolved_config_file = cached_file(
                           ^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 318, in cached_file
    file = cached_files(path_or_repo_id=path_or_repo_id, filenames=[filename], **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 529, in cached_files
    _get_cache_file_to_return(path_or_repo_id, filename, cache_dir, revision, repo_type)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/utils/hub.py", line 144, in _get_cache_file_to_return
    resolved_file = try_to_load_from_cache(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/predict.py", line 114, in _initialize_model
    self.plm, self.tokenizer, self.model_config, self.WrapperClass = load_plm_from_config(
                                                                     ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/utils.py", line 132, in load_plm_from_config
    model_config = BertConfig.from_pretrained(model_path)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 609, in from_pretrained
    config_dict, kwargs = cls.get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 649, in get_config_dict
    config_dict, kwargs = cls._get_config_dict(pretrained_model_name_or_path, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/htc/lib/python3.12/site-packages/transformers/configuration_utils.py", line 731, in _get_config_dict
    raise OSError(
OSError: Can't load the configuration of '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext' is the correct path to a directory containing a config.json file
❌ 分类器初始化失败: Can't load the configuration of '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext' is the correct path to a directory containing a config.json file
--- Log Start: 2025-08-29 18:07:48 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 6602 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 31378 ------------
length dataset['train']: 31378
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([30, 768])
depth 3: torch.Size([219, 768])
depth 4: torch.Size([904, 768])
depth 5: torch.Size([3732, 768])
depth 6: torch.Size([9578, 768])
depth 7: torch.Size([16007, 768])
depth 8: torch.Size([16056, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
✅ 启用了DataParallel，使用4张GPU进行推理
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 30, 219, 904, 3732, 9578, 16007, 16056]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 6602 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 6597
   - 总知识点关联数: 6602
🚀 开始批量预测...
🔄 开始批量预测 6597 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 6597
   - 成功预测数量: 6597
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 82.99%
   - 完全匹配题目: 5475
   - 部分匹配题目: 0
   - 无匹配题目: 1122

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 2
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 6592 题）：
   - 命中 1 个知识点: 5470题，占比 82.98%
   - 未命中任何知识点: 1122题，占比 17.02%

🧩 题目含 2 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 5题，占比 100.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-3.json

🏁 测试完成！
