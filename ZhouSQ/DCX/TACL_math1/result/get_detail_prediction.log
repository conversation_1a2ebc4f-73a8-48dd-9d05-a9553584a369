--- Log Start: 2025-08-12 14:11:28 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 11171 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 11171 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 11171
   - 总知识点关联数: 11171
🚀 开始批量预测...
🔄 开始批量预测 11171 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 11171
   - 成功预测数量: 11171
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 76.29%
   - 完全匹配题目: 8522
   - 部分匹配题目: 0
   - 无匹配题目: 2649

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 11171 题）：
   - 命中 1 个知识点: 8522题，占比 76.29%
   - 未命中任何知识点: 2649题，占比 23.71%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！


📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 11171
   - 成功预测数量: 11171
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 76.61%
   - 完全匹配题目: 8558
   - 部分匹配题目: 0
   - 无匹配题目: 2613

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 11171 题）：
   - 命中 1 个知识点: 8558题，占比 76.61%
   - 未命中任何知识点: 2613题，占比 23.39%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-3.json

🏁 测试完成！
--- Log Start: 2025-08-14 16:28:44 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 1024])
depth 1: torch.Size([5, 1024])
depth 2: torch.Size([29, 1024])
depth 3: torch.Size([217, 1024])
depth 4: torch.Size([893, 1024])
depth 5: torch.Size([3577, 1024])
depth 6: torch.Size([9097, 1024])
depth 7: torch.Size([14989, 1024])
depth 8: torch.Size([15008, 1024])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 23.46%
   - 完全匹配题目: 2470
   - 部分匹配题目: 0
   - 无匹配题目: 8059

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 2470题，占比 23.46%
   - 未命中任何知识点: 8059题，占比 76.54%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-15 16:36:10 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 82.85%
   - 完全匹配题目: 8723
   - 部分匹配题目: 0
   - 无匹配题目: 1806

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 8723题，占比 82.85%
   - 未命中任何知识点: 1806题，占比 17.15%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
--- Log Start: 2025-08-15 17:45:38 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 81.10%
   - 完全匹配题目: 8539
   - 部分匹配题目: 0
   - 无匹配题目: 1990

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 8539题，占比 81.10%
   - 未命中任何知识点: 1990题，占比 18.90%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！
--- Log Start: 2025-08-18 09:45:19 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 64.21%
   - 完全匹配题目: 6761
   - 部分匹配题目: 0
   - 无匹配题目: 3768

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 6761题，占比 64.21%
   - 未命中任何知识点: 3768题，占比 35.79%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-19 09:26:09 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 10529 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 29259 ------------
length dataset['train']: 29259
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_math1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([5, 768])
depth 2: torch.Size([29, 768])
depth 3: torch.Size([217, 768])
depth 4: torch.Size([893, 768])
depth 5: torch.Size([3577, 768])
depth 6: torch.Size([9097, 768])
depth 7: torch.Size([14989, 768])
depth 8: torch.Size([15008, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
✅ 启用了DataParallel，使用4张GPU进行推理
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持9层分层分类
📊 各层标签数量: [1, 5, 29, 217, 893, 3577, 9097, 14989, 15008]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 10529 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 10529
   - 总知识点关联数: 10529
🚀 开始批量预测...
🔄 开始批量预测 10529 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_math1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 10529
   - 成功预测数量: 10529
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 45.29%
   - 完全匹配题目: 4769
   - 部分匹配题目: 0
   - 无匹配题目: 5760

📚 知识点数量分析:
   - 平均知识点数量: 1.00
   - 最多知识点数量: 1
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 10529 题）：
   - 命中 1 个知识点: 4769题，占比 45.29%
   - 未命中任何知识点: 5760题，占比 54.71%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_math1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
